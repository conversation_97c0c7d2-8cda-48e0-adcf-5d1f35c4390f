import { useEffect, useState } from "react";
import { useGetStoreList } from "../store/storeListHooks";
import { useGetSKUList } from "../sku/skuListHooks";
import { useGetForecastAlgorithms } from "./forecastAlgorithmHooks";
import { useGetForecastFactors } from "./forecastFactorHooks";

export const useForecastMeta = () => {
  const [isMetaLoading, setIsMetaLoading] = useState(true);
  const [meta, setMeta] = useState({
    stores: [],
    skus: [],
    algorithms: [],
    factors: [],
    defaultAlgorithm: "", // 👈 add this here
  });

  const {
    data: storeData,
    isLoading: isStoresLoading,
    refetch: refetchStores,
  } = useGetStoreList();

  const {
    data: skuData,
    isLoading: isSKULoading,
    refetch: refetchSKUs,
  } = useGetSKUList();

  const {
    data: algoData,
    isLoading: isAlgoLoading,
    refetch: refetchAlgos,
  } = useGetForecastAlgorithms();

  const {
    data: factorData,
    isLoading: isFactorsLoading,
    refetch: refetchFactors,
  } = useGetForecastFactors();

  useEffect(() => {
    const fetchAll = async () => {
      setIsMetaLoading(true);
      try {
        const [storeRes, skuRes, algoRes, factorRes] = await Promise.all([
          refetchStores(),
          refetchSKUs(),
          refetchAlgos(),
          refetchFactors(),
        ]);

        const algorithms = algoRes.data?.result?.data || [];
        const defaultAlgo = algorithms.find(
          (a) => a.algorithm_name === "prophet"
        );

        setMeta({
          stores: storeRes.data?.result?.data || [],
          skus: skuRes.data?.result?.data || [],
          algorithms,
          factors: factorRes.data?.result?.data || [],
          defaultAlgorithm: defaultAlgo?.algorithm_name || "",
        });
      } catch (err) {
        console.error("Error loading metadata:", err);
      } finally {
        setIsMetaLoading(false);
      }
    };

    fetchAll();
  }, []);

  return {
    ...meta,
    isMetaLoading:
      isStoresLoading ||
      isSKULoading ||
      isAlgoLoading ||
      isFactorsLoading ||
      isMetaLoading,
  };
};
